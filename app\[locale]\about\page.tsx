import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { isValidLocale } from '@/lib/i18n'
import AboutClient from './components/AboutClient'

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }): Promise<Metadata> {
  if (!isValidLocale(locale)) {
    notFound();
  }

  const isEnglish = locale === 'en';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://0dot.com';

  return {
    title: isEnglish
      ? 'About Us - 0dot | Enterprise AI & Cloud Computing Solutions Provider'
      : '关于我们 - 0dot | 企业级AI与云计算解决方案提供商',
    description: isEnglish
      ? 'Learn about 0dot, a leading provider of enterprise AI and cloud computing solutions. Founded in 2025, we specialize in AI annotation, CPU rental, education management, and custom software development.'
      : '了解0dot，领先的企业级AI与云计算解决方案提供商。成立于2025年，专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发。',
    keywords: isEnglish
      ? ['0dot about', 'enterprise AI solutions', 'cloud computing provider', 'AI annotation company', 'CPU rental services', 'education management systems', 'custom software development', 'technology company', 'digital transformation', 'artificial intelligence']
      : ['0dot关于我们', '企业AI解决方案', '云计算服务商', 'AI标注公司', 'CPU算力租用', '教育管理系统', '定制软件开发', '科技公司', '数字化转型', '人工智能'],
    openGraph: {
      title: isEnglish
        ? 'About Us - 0dot | Enterprise AI & Cloud Computing Solutions Provider'
        : '关于我们 - 0dot | 企业级AI与云计算解决方案提供商',
      description: isEnglish
        ? 'Learn about 0dot, a leading provider of enterprise AI and cloud computing solutions. Founded in 2025, we specialize in AI annotation, CPU rental, education management, and custom software development.'
        : '了解0dot，领先的企业级AI与云计算解决方案提供商。成立于2025年，专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发。',
      url: `${baseUrl}/${locale}/about`,
      siteName: '0dot',
      images: [
        {
          url: `${baseUrl}/og-about.jpg`,
          width: 1200,
          height: 630,
          alt: isEnglish ? '0dot About Us' : '0dot关于我们',
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: isEnglish
        ? 'About Us - 0dot | Enterprise AI & Cloud Computing Solutions Provider'
        : '关于我们 - 0dot | 企业级AI与云计算解决方案提供商',
      description: isEnglish
        ? 'Learn about 0dot, a leading provider of enterprise AI and cloud computing solutions.'
        : '了解0dot，领先的企业级AI与云计算解决方案提供商。',
      images: [`${baseUrl}/og-about.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/about`,
      languages: {
        'zh-CN': `${baseUrl}/about`,
        'en-US': `${baseUrl}/en/about`,
      },
    },
  };
}

export default function About({ params: { locale } }: { params: { locale: string } }) {
  if (!isValidLocale(locale)) {
    notFound();
  }

  return <AboutClient />;
}}