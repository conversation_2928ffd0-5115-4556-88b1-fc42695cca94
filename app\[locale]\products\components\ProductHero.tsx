'use client'

import { motion } from "framer-motion"
import { <PERSON><PERSON>les, Zap, Target, ArrowRight, Star, TrendingUp, Shield } from "lucide-react"
import { useTranslations } from '@/hooks/useTranslations'

export function ProductHero() {
  const t = useTranslations('productsPage')
  const tCommon = useTranslations('common')

  return (
    <section className="relative py-20 sm:py-28 overflow-hidden">
      {/* 增强的背景层 */}
      <div className="absolute inset-0 -z-10">
        {/* 主背景渐变 */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/50 to-indigo-50/30" />

        {/* 动态光效 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-indigo-400/8 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        </div>

        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.15) 1px, transparent 0)`,
            backgroundSize: '40px 40px'
          }} />
        </div>

        {/* 渐变遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/20 to-white/40" />
      </div>

      {/* 浮动装饰元素 */}
      <div className="absolute top-20 left-10 w-72 h-72 rounded-full blur-3xl animate-float opacity-30" style={{ background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.08), rgba(37, 99, 235, 0.04))' }} />
      <div className="absolute bottom-20 right-10 w-96 h-96 rounded-full blur-3xl animate-float opacity-25" style={{ background: 'linear-gradient(135deg, rgba(37, 99, 235, 0.06), rgba(59, 130, 246, 0.03))', animationDelay: '3s' }} />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full blur-3xl animate-float opacity-20" style={{ background: 'radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, transparent 70%)', animationDelay: '1.5s' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-5xl text-center">
          {/* 增强的标题区域 */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            {/* 标签徽章 */}
            <motion.div
              className="inline-flex items-center gap-2 px-5 py-2.5 rounded-full backdrop-blur-md border mb-8 shadow-lg"
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderColor: 'rgba(59, 130, 246, 0.3)',
                boxShadow: '0 8px 32px rgba(59, 130, 246, 0.15)'
              }}
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Sparkles className="w-4 h-4" style={{ color: 'rgb(59 130 246)' }} />
              <span className="text-sm font-semibold" style={{ color: 'rgb(59 130 246)' }}>{t('heroTag')}</span>
              <ArrowRight className="w-3 h-3 ml-1" style={{ color: 'rgb(59 130 246)' }} />
            </motion.div>

            {/* 主标题 */}
            <motion.h1
              className="text-5xl font-bold tracking-tight sm:text-6xl lg:text-7xl xl:text-8xl mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              <span className="block text-gradient-modern">
                {t('title')}
              </span>
            </motion.h1>

            {/* 副标题 */}
            <motion.p
              className="text-xl sm:text-2xl leading-relaxed text-slate-600 max-w-4xl mx-auto mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              {t('subtitle')}
            </motion.p>

            {/* 统计数据 */}
            <motion.div
              className="flex flex-wrap justify-center gap-8 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.6 }}
            >
              {[
                { icon: Star, number: "50+", label: t('stats.qualityProducts') },
                { icon: TrendingUp, number: "99%", label: t('stats.satisfaction') },
                { icon: Shield, number: "24/7", label: t('stats.support') }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-3 px-4 py-2 rounded-xl bg-white/60 backdrop-blur-sm border border-white/40"
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <div className="p-2 rounded-lg bg-blue-50">
                    <stat.icon className="w-4 h-4 text-blue-600" />
                  </div>
                  <div className="text-left">
                    <div className="text-lg font-bold text-slate-800">{stat.number}</div>
                    <div className="text-xs text-slate-600">{stat.label}</div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* 重新设计的特色亮点 */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-5xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.0 }}
          >
            {[
              {
                icon: Target,
                title: t('features.precision'),
                description: t('features.precisionDesc'),
                gradient: 'from-blue-500 to-cyan-500',
                bgGradient: 'from-blue-50 to-cyan-50'
              },
              {
                icon: Zap,
                title: t('features.deployment'),
                description: t('features.deploymentDesc'),
                gradient: 'from-indigo-500 to-purple-500',
                bgGradient: 'from-indigo-50 to-purple-50'
              },
              {
                icon: Sparkles,
                title: t('features.innovation'),
                description: t('features.innovationDesc'),
                gradient: 'from-purple-500 to-pink-500',
                bgGradient: 'from-purple-50 to-pink-50'
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                className="group relative"
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 1.2 + index * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                {/* 卡片主体 */}
                <div className={`relative flex flex-col items-center text-center p-8 rounded-3xl bg-gradient-to-br ${item.bgGradient} border border-white/60 backdrop-blur-sm shadow-lg group-hover:shadow-2xl transition-all duration-500 overflow-hidden`}>

                  {/* 背景装饰 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/40 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-white/20 to-transparent rounded-full blur-2xl" />

                  {/* 图标容器 */}
                  <motion.div
                    className={`relative p-4 rounded-2xl mb-6 bg-gradient-to-br ${item.gradient} shadow-lg group-hover:shadow-xl transition-all duration-500`}
                    whileHover={{ rotate: [0, -5, 5, 0], scale: 1.1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <item.icon className="w-8 h-8 text-white relative z-10" />

                    {/* 图标光晕 */}
                    <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${item.gradient} opacity-50 blur-lg group-hover:opacity-75 transition-opacity duration-500`} />
                  </motion.div>

                  {/* 内容 */}
                  <div className="relative z-10">
                    <h3 className="text-xl font-bold text-slate-800 mb-3 group-hover:text-slate-900 transition-colors duration-300">
                      {item.title}
                    </h3>
                    <p className="text-slate-600 leading-relaxed group-hover:text-slate-700 transition-colors duration-300">
                      {item.description}
                    </p>
                  </div>

                  {/* 悬停时的边框光效 */}
                  <div className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${item.gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-500 -z-10 blur-xl`} />
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}
