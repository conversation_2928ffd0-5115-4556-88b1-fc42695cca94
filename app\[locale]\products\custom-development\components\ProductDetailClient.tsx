"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useTranslations } from '@/hooks/useTranslations'
import {
  Code,
  Globe,
  Smartphone,
  Building,
  Settings,
  CheckCircle,
  Star,
  TrendingUp,
  BarChart,
  Shield,
  Zap,
  Users,
  ArrowRight,
  Sparkles,
  Target,
  Award,
  Clock,
  HeartHandshake
} from "lucide-react"

interface ProductDetail {
  name: string
  description: string
  features: string[]
  techSpecs: {
    deployment: string
    security: string
    availability: string
    support: string
  }
  featureList?: Array<{
    title: string
    description: string
    features: Array<{
      name: string
      description: string
      icon: string
    }>
  }>
  demoVideo?: {
    url: string
    thumbnail: string
  }
  benefits?: Array<{
    title: string
    description: string
  }>
}

interface Props {
  product: ProductDetail
  productSlug: string
}

const iconMap = {
  Code,
  Globe,
  Smartphone,
  Building,
  Settings,
  CheckCircle,
  Star,
  TrendingUp,
  BarChart,
  Shield,
  Zap,
  Users,
  Target,
  Award,
  Clock,
  HeartHandshake
}

export function ProductDetailClient({ product, productSlug }: Props) {
  const t = useTranslations('productDetails.customDevelopment')
  const tCommon = useTranslations('common')

  const renderIcon = (iconName: string, className?: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap]
    if (!IconComponent) {
      return <div className={`bg-orange-500/20 rounded ${className}`} />
    }
    return <IconComponent className={className} />
  }

  return (
    <div className="relative isolate">
      {/* Hero Section */}
      <section className="relative py-20 sm:py-24 lg:py-32 bg-gradient-to-br from-orange-50/50 via-white to-red-50/30 overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-20 left-10 w-32 h-32 bg-orange-100/30 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-20 right-10 w-40 h-40 bg-red-100/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
          <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-pink-100/20 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '4s' }} />
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-4xl text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge className="mb-6 px-4 py-2 bg-gradient-to-r from-orange-100 to-red-100 text-orange-700 border-orange-200">
                <Code className="w-4 h-4 mr-2" />
{t('customDevelopmentServices')}
              </Badge>
            </motion.div>

            <motion.h1
              className="text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl lg:text-7xl mb-6"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <span className="bg-gradient-to-r from-orange-600 via-red-600 to-pink-600 bg-clip-text text-transparent">
                {product.name}
              </span>
            </motion.h1>

            <motion.p
              className="text-lg sm:text-xl text-slate-600 mb-8 leading-relaxed max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
            >
              {product.description}
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <Button size="lg" className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white px-8 py-3 rounded-xl font-semibold shadow-lg">
                <Sparkles className="w-5 h-5 mr-2" />
                {t('consultNow')}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button size="lg" variant="outline" className="border-orange-200 text-orange-700 hover:bg-orange-50 px-8 py-3 rounded-xl font-semibold">
                <Target className="w-5 h-5 mr-2" />
                {t('viewCases')}
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Features Section */}
      <section className="py-20 sm:py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-slate-900 sm:text-4xl mb-4">
              {t('coreDevelopmentCapabilities')}
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              {t('coreDevelopmentDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: "Globe",
                title: t('webDevelopment'),
                description: t('webDevelopmentDesc'),
                color: "from-blue-500 to-blue-600"
              },
              {
                icon: "Smartphone",
                title: t('mobileDevelopment'),
                description: t('mobileDevelopmentDesc'),
                color: "from-green-500 to-green-600"
              },
              {
                icon: "Building",
                title: t('enterpriseSoftware'),
                description: t('enterpriseSoftwareDesc'),
                color: "from-purple-500 to-purple-600"
              },
              {
                icon: "Settings",
                title: t('systemIntegration'),
                description: t('systemIntegrationDesc'),
                color: "from-orange-500 to-orange-600"
              }
            ].map((feature, index) => (
              <motion.div
                key={feature.title}
                className="relative group"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -8 }}
              >
                <div className="relative p-8 bg-white rounded-2xl border border-slate-200 shadow-sm hover:shadow-xl transition-all duration-300 h-full">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    {renderIcon(feature.icon, "w-6 h-6 text-white")}
                  </div>
                  <h3 className="text-xl font-semibold text-slate-900 mb-3">{feature.title}</h3>
                  <p className="text-slate-600 leading-relaxed">{feature.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Technical Specifications */}
      <section className="py-20 sm:py-24 bg-gradient-to-br from-slate-50 to-blue-50/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-slate-900 sm:text-4xl mb-4">
              {t('techSpecs')}
            </h2>
            <p className="text-lg text-slate-600">
              {t('techSpecsDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Object.entries(product.techSpecs).map(([key, value], index) => (
              <motion.div
                key={key}
                className="bg-white p-6 rounded-xl border border-slate-200 shadow-sm hover:shadow-md transition-shadow duration-300"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center">
                    {renderIcon("CheckCircle", "w-4 h-4 text-white")}
                  </div>
                  <h3 className="font-semibold text-slate-900 capitalize">{key}</h3>
                </div>
                <p className="text-slate-600">{value}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 sm:py-24 bg-gradient-to-r from-orange-500 via-red-600 to-pink-600">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold text-white sm:text-4xl mb-4">
              {t('readyToStartProject')}
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              {t('ctaDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-white text-orange-600 hover:bg-orange-50 px-8 py-3 rounded-xl font-semibold">
                <HeartHandshake className="w-5 h-5 mr-2" />
                {t('consultNow')}
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10 px-8 py-3 rounded-xl font-semibold">
                <Award className="w-5 h-5 mr-2" />
                {t('viewMoreCases')}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
