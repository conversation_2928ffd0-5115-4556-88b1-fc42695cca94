const fs = require('fs');
const path = require('path');

// 读取模块文件的函数
function readModuleFile(locale, moduleName) {
  const filePath = path.join(__dirname, 'modules', locale, `${moduleName}.json`);
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.warn(`Warning: Could not read ${filePath}:`, error.message);
    return {};
  }
}

// 构建完整翻译文件的函数
function buildMessages(locale) {
  const modules = [
    'common',
    'navigation',
    'hero',
    'services',
    'products',
    'product-details',
    'stats',
    'home',
    'about',
    'products-page',
    'ai-annotation',
    'cpu-rental',
    'education-management',
    'custom-development',
    'contact',
    'contactUs',
    'footer'
  ];

  const messages = {};

  modules.forEach(moduleName => {
    const moduleData = readModuleFile(locale, moduleName);
    
    // 根据模块名称决定如何合并数据
    switch (moduleName) {
      case 'products-page':
        messages.productsPage = moduleData;
        break;
      case 'product-details':
        messages.productDetails = moduleData;
        break;
      case 'ai-annotation':
        if (!messages.productDetails) messages.productDetails = {};
        messages.productDetails.aiAnnotation = {
          ...messages.productDetails.aiAnnotation,
          ...moduleData
        };
        break;
      case 'cpu-rental':
        if (!messages.productDetails) messages.productDetails = {};
        messages.productDetails.cpuRental = {
          ...messages.productDetails.cpuRental,
          ...moduleData
        };
        break;
      case 'education-management':
        if (!messages.productDetails) messages.productDetails = {};
        messages.productDetails.educationManagement = {
          ...messages.productDetails.educationManagement,
          ...moduleData
        };
        break;
      case 'custom-development':
        if (!messages.productDetails) messages.productDetails = {};
        messages.productDetails.customDevelopment = {
          ...messages.productDetails.customDevelopment,
          ...moduleData
        };
        break;
      default:
        messages[moduleName] = moduleData;
    }
  });

  return messages;
}

// 构建英文和中文翻译文件
const enMessages = buildMessages('en');
const zhMessages = buildMessages('zh');

// 写入文件
fs.writeFileSync(
  path.join(__dirname, 'en.json'),
  JSON.stringify(enMessages, null, 2),
  'utf8'
);

fs.writeFileSync(
  path.join(__dirname, 'zh.json'),
  JSON.stringify(zhMessages, null, 2),
  'utf8'
);

console.log('✅ Messages built successfully!');
console.log('📁 Generated files:');
console.log('  - messages/en.json');
console.log('  - messages/zh.json');
