'use client'

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, MessageCircle, Calendar, Phone, Sparkles, Star, Shield, Zap } from "lucide-react"
import Link from "next/link"
import { useTranslations } from "@/hooks/useTranslations"

export function ProductCTA() {
  const t = useTranslations('productsPage.cta')
  const tCommon = useTranslations('common')
  return (
    <section className="py-20 sm:py-28 relative overflow-hidden">
      {/* 增强的背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900" />

      {/* 动态光效层 */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.4)_0%,transparent_50%),radial-gradient(circle_at_70%_80%,rgba(147,51,234,0.3)_0%,transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.1)_0%,transparent_70%)]" />
      </div>

      {/* 网格背景 */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.15) 1px, transparent 0)`,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* 浮动装饰元素 */}
      <div className="absolute top-20 left-10 w-80 h-80 rounded-full blur-3xl animate-float opacity-25" style={{ background: 'linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(147, 51, 234, 0.3))' }} />
      <div className="absolute bottom-20 right-10 w-96 h-96 rounded-full blur-3xl animate-float opacity-20" style={{ background: 'linear-gradient(135deg, rgba(147, 51, 234, 0.3), rgba(59, 130, 246, 0.2))', animationDelay: '3s' }} />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full blur-3xl animate-float opacity-15" style={{ background: 'radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%)', animationDelay: '1.5s' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-5xl text-center">
          {/* 重新设计的主标题 */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            viewport={{ once: true }}
          >
            {/* 标签徽章 */}
            <motion.div
              className="inline-flex items-center gap-2 px-5 py-2.5 rounded-full backdrop-blur-md border mb-8 shadow-lg"
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderColor: 'rgba(255, 255, 255, 0.3)',
                boxShadow: '0 8px 32px rgba(59, 130, 246, 0.3)'
              }}
              initial={{ scale: 0.9, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.05 }}
            >
              <Sparkles className="w-4 h-4 text-blue-300" />
              <span className="text-sm font-semibold text-blue-100">{t('startDigitalJourney')}</span>
              <ArrowRight className="w-3 h-3 ml-1 text-blue-300" />
            </motion.div>

            {/* 主标题 */}
            <motion.h2
              className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl xl:text-7xl text-white mb-8"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              {t('title')}
              <motion.span
                className="block bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent"
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                viewport={{ once: true }}
              >
                {t('titleHighlight')}
              </motion.span>
            </motion.h2>

            {/* 副标题 */}
            <motion.p
              className="text-xl sm:text-2xl leading-relaxed text-blue-100 max-w-4xl mx-auto mb-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              viewport={{ once: true }}
            >
              {t('subtitle')}
            </motion.p>

            {/* 特色亮点 */}
            <motion.div
              className="flex flex-wrap justify-center gap-6 mb-12"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
              viewport={{ once: true }}
            >
              {[
                { icon: Star, text: t('fiveStarRating') },
                { icon: Shield, text: t('secureReliable') },
                { icon: Zap, text: t('rapidDeployment') }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-2 px-4 py-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20"
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <item.icon className="w-4 h-4 text-blue-300" />
                  <span className="text-sm font-medium text-blue-100">{item.text}</span>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* 重新设计的CTA按钮组 */}
          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-20"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
            viewport={{ once: true }}
          >
            {/* 主要CTA按钮 */}
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Button asChild className="relative group text-lg px-10 py-5 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 rounded-2xl shadow-2xl overflow-hidden">
                <Link href="/contact-us">
                  {/* 按钮背景光效 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                  {/* 按钮内容 */}
                  <span className="relative z-10 flex items-center gap-3">
                    <MessageCircle className="w-5 h-5" />
                    {t('contactNow')}
                    <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
                  </span>

                  {/* 光晕效果 */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 opacity-50 blur-xl group-hover:opacity-75 transition-opacity duration-500 -z-10" />
                </Link>
              </Button>
            </motion.div>

            {/* 次要CTA按钮 */}
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Button
                variant="outline"
                className="relative group px-10 py-5 text-lg bg-white/10 backdrop-blur-md border-2 border-white/30 hover:bg-white/20 hover:border-white/50 text-white hover:text-white rounded-2xl transition-all duration-500 overflow-hidden"
              >
                {/* 按钮背景 */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* 按钮内容 */}
                <span className="relative z-10 flex items-center gap-3">
                  <Calendar className="w-5 h-5" />
                  {t('bookDemo')}
                </span>

                {/* 边框光效 */}
                <div className="absolute inset-0 rounded-2xl border-2 border-white/50 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              </Button>
            </motion.div>
          </motion.div>

          {/* 重新设计的联系方式 */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.4 }}
            viewport={{ once: true }}
          >
            {[
              {
                icon: MessageCircle,
                title: t('contact.online.title'),
                description: t('contact.online.description'),
                action: t('contact.online.action'),
                gradient: "from-blue-500 to-cyan-500",
                bgGradient: "from-blue-500/10 to-cyan-500/10"
              },
              {
                icon: Phone,
                title: t('contact.phone.title'),
                description: t('contact.phone.description'),
                action: t('contact.phone.action'),
                gradient: "from-green-500 to-emerald-500",
                bgGradient: "from-green-500/10 to-emerald-500/10"
              },
              {
                icon: Calendar,
                title: t('contact.meeting.title'),
                description: t('contact.meeting.description'),
                action: t('contact.meeting.action'),
                gradient: "from-purple-500 to-pink-500",
                bgGradient: "from-purple-500/10 to-pink-500/10"
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                className="group relative"
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 1.6 + index * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                viewport={{ once: true }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                {/* 主卡片 */}
                <div className={`relative p-8 rounded-3xl bg-gradient-to-br ${item.bgGradient} backdrop-blur-md border border-white/20 hover:border-white/40 transition-all duration-500 overflow-hidden text-center group-hover:shadow-2xl`}>

                  {/* 背景装饰 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-white/10 to-transparent rounded-full blur-2xl" />

                  {/* 图标容器 */}
                  <motion.div
                    className="relative mx-auto mb-6"
                    whileHover={{ rotate: [0, -10, 10, 0], scale: 1.1 }}
                    transition={{ duration: 0.6 }}
                  >
                    <div className="relative w-16 h-16 mx-auto">
                      {/* 图标背景光晕 */}
                      <div
                        className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${item.gradient} opacity-30 group-hover:opacity-50 transition-all duration-500 blur-lg`}
                      />

                      {/* 图标主体 */}
                      <div className={`relative flex items-center justify-center w-full h-full rounded-2xl bg-gradient-to-br ${item.gradient} shadow-lg group-hover:shadow-xl transition-all duration-500`}>
                        <item.icon className="w-8 h-8 text-white relative z-10" />
                      </div>

                      {/* 外部光环 */}
                      <div
                        className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${item.gradient} opacity-0 group-hover:opacity-40 transition-opacity duration-500 -z-10 blur-xl scale-110`}
                      />
                    </div>
                  </motion.div>

                  {/* 内容区域 */}
                  <div className="relative z-10">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-blue-100 transition-colors duration-300">
                      {item.title}
                    </h3>
                    <p className="text-blue-100 leading-relaxed mb-6 group-hover:text-white transition-colors duration-300">
                      {item.description}
                    </p>

                    {/* 行动按钮 */}
                    <motion.button
                      className="inline-flex items-center gap-2 px-4 py-2 rounded-xl bg-white/20 hover:bg-white/30 text-white font-medium transition-all duration-300 border border-white/30 hover:border-white/50"
                      whileHover={{ scale: 1.05, x: 5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <span className="text-sm">{item.action}</span>
                      <ArrowRight className="w-4 h-4" />
                    </motion.button>
                  </div>

                  {/* 悬停时的边框光效 */}
                  <div
                    className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${item.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500 -z-10 blur-xl`}
                  />
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* 重新设计的底部信任标识 */}
          <motion.div
            className="mt-20 pt-10 border-t border-white/20"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 2.0 }}
            viewport={{ once: true }}
          >
            {/* 信任标识标题 */}
            <motion.div
              className="text-center mb-8"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 2.2 }}
              viewport={{ once: true }}
            >
              <p className="text-blue-200 text-sm font-medium mb-4">{t('trustedByGlobalEnterprises')}</p>
              <div className="flex justify-center items-center gap-2">
                <div className="w-8 h-px bg-gradient-to-r from-transparent to-blue-400"></div>
                <Shield className="w-4 h-4 text-blue-400" />
                <div className="w-8 h-px bg-gradient-to-l from-transparent to-blue-400"></div>
              </div>
            </motion.div>

            {/* 信任标识网格 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
              {[
                {
                  icon: Shield,
                  text: t('trust.iso'),
                  color: "from-green-400 to-emerald-400"
                },
                {
                  icon: Star,
                  text: t('trust.soc2'),
                  color: "from-blue-400 to-cyan-400"
                },
                {
                  icon: Sparkles,
                  text: t('trust.gdpr'),
                  color: "from-purple-400 to-pink-400"
                },
                {
                  icon: Zap,
                  text: t('trust.support'),
                  color: "from-orange-400 to-red-400"
                }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="group flex flex-col items-center gap-3 p-4 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.6,
                    delay: 2.4 + index * 0.1
                  }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.05, y: -2 }}
                >
                  {/* 图标 */}
                  <div className="relative">
                    <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${item.color} flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                      <item.icon className="w-4 h-4 text-white" />
                    </div>

                    {/* 状态指示器 */}
                    <div className="absolute -top-1 -right-1">
                      <div className="w-3 h-3 rounded-full bg-green-400 border-2 border-white/20">
                        <div className="w-full h-full rounded-full bg-green-400 animate-pulse"></div>
                      </div>
                    </div>
                  </div>

                  {/* 文本 */}
                  <span className="text-xs font-medium text-blue-100 group-hover:text-white transition-colors duration-300 text-center">
                    {item.text}
                  </span>
                </motion.div>
              ))}
            </div>

            {/* 底部说明 */}
            <motion.div
              className="mt-8 text-center"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 2.8 }}
              viewport={{ once: true }}
            >
              <p className="text-xs text-blue-200/80">
                {t('internationalCertification')}
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
